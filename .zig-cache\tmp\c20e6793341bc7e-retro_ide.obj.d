.zig-cache\tmp\c20e6793341bc7e-retro_ide.obj: ddgbas/retro_ide.c \
  C:/Development/zig/lib/libc/include/any-windows-any/stdio.h \
  C:/Development/zig/lib/libc/include/any-windows-any/corecrt_stdio_config.h \
  C:/Development/zig/lib/libc/include/any-windows-any/corecrt.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_mingw.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_mingw_mac.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_mingw_secapi.h \
  C:/Development/zig/lib/include/vadefs.h \
  C:/Development/zig/lib/libc/include/any-windows-any/vadefs.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sdks/_mingw_ddk.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_mingw_off_t.h \
  C:/Development/zig/lib/libc/include/any-windows-any/swprintf.inl \
  C:/Development/zig/lib/libc/include/any-windows-any/sec_api/stdio_s.h \
  C:/Development/zig/lib/libc/include/any-windows-any/stdlib.h \
  C:/Development/zig/lib/libc/include/any-windows-any/corecrt_wstdlib.h \
  C:/Development/zig/lib/include/limits.h \
  C:/Development/zig/lib/libc/include/any-windows-any/limits.h \
  C:/Development/zig/lib/libc/include/any-windows-any/crtdefs.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sec_api/stdlib_s.h \
  C:/Development/zig/lib/libc/include/any-windows-any/malloc.h \
  C:/Development/zig/lib/include/mm_malloc.h \
  C:/Development/zig/lib/libc/include/any-windows-any/string.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sec_api/string_s.h \
  C:/Development/zig/lib/libc/include/any-windows-any/ctype.h \
  C:/Development/zig/lib/libc/include/any-windows-any/corecrt_wctype.h \
  C:/Development/zig/lib/libc/include/any-windows-any/time.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sys/timeb.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sec_api/sys/timeb_s.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_timeval.h \
  C:/Development/zig/lib/libc/include/any-windows-any/pthread_time.h \
  C:/Development/zig/lib/libc/include/any-windows-any/pthread_compat.h \
  C:/Development/zig/lib/libc/include/any-windows-any/windows.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sdkddkver.h \
  C:/Development/zig/lib/libc/include/any-windows-any/excpt.h \
  C:/Development/zig/lib/include/stdarg.h \
  C:/Development/zig/lib/libc/include/any-windows-any/stdarg.h \
  C:/Development/zig/lib/include/__stdarg_header_macro.h \
  C:/Development/zig/lib/include/__stdarg___gnuc_va_list.h \
  C:/Development/zig/lib/include/__stdarg_va_list.h \
  C:/Development/zig/lib/include/__stdarg_va_arg.h \
  C:/Development/zig/lib/include/__stdarg___va_copy.h \
  C:/Development/zig/lib/include/__stdarg_va_copy.h \
  C:/Development/zig/lib/libc/include/any-windows-any/windef.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winapifamily.h \
  C:/Development/zig/lib/libc/include/any-windows-any/minwindef.h \
  C:/Development/zig/lib/libc/include/any-windows-any/specstrings.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sal.h \
  C:/Development/zig/lib/libc/include/any-windows-any/concurrencysal.h \
  C:/Development/zig/lib/libc/include/any-windows-any/driverspecs.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winnt.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_mingw_unicode.h \
  C:/Development/zig/lib/libc/include/any-windows-any/apiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/intrin-impl.h \
  C:/Development/zig/lib/libc/include/any-windows-any/basetsd.h \
  C:/Development/zig/lib/libc/include/any-windows-any/guiddef.h \
  C:/Development/zig/lib/include/x86intrin.h \
  C:/Development/zig/lib/include/ia32intrin.h \
  C:/Development/zig/lib/include/immintrin.h \
  C:/Development/zig/lib/include/x86gprintrin.h \
  C:/Development/zig/lib/include/hresetintrin.h \
  C:/Development/zig/lib/include/uintrintrin.h \
  C:/Development/zig/lib/include/usermsrintrin.h \
  C:/Development/zig/lib/include/crc32intrin.h \
  C:/Development/zig/lib/include/prfchiintrin.h \
  C:/Development/zig/lib/include/raointintrin.h \
  C:/Development/zig/lib/include/cmpccxaddintrin.h \
  C:/Development/zig/lib/include/mmintrin.h \
  C:/Development/zig/lib/include/xmmintrin.h \
  C:/Development/zig/lib/include/emmintrin.h \
  C:/Development/zig/lib/include/pmmintrin.h \
  C:/Development/zig/lib/include/tmmintrin.h \
  C:/Development/zig/lib/include/smmintrin.h \
  C:/Development/zig/lib/include/popcntintrin.h \
  C:/Development/zig/lib/include/wmmintrin.h \
  C:/Development/zig/lib/include/__wmmintrin_aes.h \
  C:/Development/zig/lib/include/__wmmintrin_pclmul.h \
  C:/Development/zig/lib/include/clflushoptintrin.h \
  C:/Development/zig/lib/include/clwbintrin.h \
  C:/Development/zig/lib/include/avxintrin.h \
  C:/Development/zig/lib/include/avx2intrin.h \
  C:/Development/zig/lib/include/f16cintrin.h \
  C:/Development/zig/lib/include/bmiintrin.h \
  C:/Development/zig/lib/include/bmi2intrin.h \
  C:/Development/zig/lib/include/lzcntintrin.h \
  C:/Development/zig/lib/include/fmaintrin.h \
  C:/Development/zig/lib/include/avx512fintrin.h \
  C:/Development/zig/lib/include/avx512vlintrin.h \
  C:/Development/zig/lib/include/avx512bwintrin.h \
  C:/Development/zig/lib/include/avx512bitalgintrin.h \
  C:/Development/zig/lib/include/avx512cdintrin.h \
  C:/Development/zig/lib/include/avx512vpopcntdqintrin.h \
  C:/Development/zig/lib/include/avx512vpopcntdqvlintrin.h \
  C:/Development/zig/lib/include/avx512vnniintrin.h \
  C:/Development/zig/lib/include/avx512vlvnniintrin.h \
  C:/Development/zig/lib/include/avxvnniintrin.h \
  C:/Development/zig/lib/include/avx512dqintrin.h \
  C:/Development/zig/lib/include/avx512vlbitalgintrin.h \
  C:/Development/zig/lib/include/avx512vlbwintrin.h \
  C:/Development/zig/lib/include/avx512vlcdintrin.h \
  C:/Development/zig/lib/include/avx512vldqintrin.h \
  C:/Development/zig/lib/include/avx512ifmaintrin.h \
  C:/Development/zig/lib/include/avx512ifmavlintrin.h \
  C:/Development/zig/lib/include/avxifmaintrin.h \
  C:/Development/zig/lib/include/avx512vbmiintrin.h \
  C:/Development/zig/lib/include/avx512vbmivlintrin.h \
  C:/Development/zig/lib/include/avx512vbmi2intrin.h \
  C:/Development/zig/lib/include/avx512vlvbmi2intrin.h \
  C:/Development/zig/lib/include/avx512fp16intrin.h \
  C:/Development/zig/lib/include/avx512vlfp16intrin.h \
  C:/Development/zig/lib/include/avx512bf16intrin.h \
  C:/Development/zig/lib/include/avx512vlbf16intrin.h \
  C:/Development/zig/lib/include/pkuintrin.h \
  C:/Development/zig/lib/include/vpclmulqdqintrin.h \
  C:/Development/zig/lib/include/vaesintrin.h \
  C:/Development/zig/lib/include/gfniintrin.h \
  C:/Development/zig/lib/include/avxvnniint8intrin.h \
  C:/Development/zig/lib/include/avxneconvertintrin.h \
  C:/Development/zig/lib/include/sha512intrin.h \
  C:/Development/zig/lib/include/sm3intrin.h \
  C:/Development/zig/lib/include/sm4intrin.h \
  C:/Development/zig/lib/include/avxvnniint16intrin.h \
  C:/Development/zig/lib/include/rtmintrin.h \
  C:/Development/zig/lib/include/xtestintrin.h \
  C:/Development/zig/lib/include/shaintrin.h \
  C:/Development/zig/lib/include/fxsrintrin.h \
  C:/Development/zig/lib/include/xsaveintrin.h \
  C:/Development/zig/lib/include/xsaveoptintrin.h \
  C:/Development/zig/lib/include/xsavecintrin.h \
  C:/Development/zig/lib/include/xsavesintrin.h \
  C:/Development/zig/lib/include/cetintrin.h \
  C:/Development/zig/lib/include/adcintrin.h \
  C:/Development/zig/lib/include/adxintrin.h \
  C:/Development/zig/lib/include/rdseedintrin.h \
  C:/Development/zig/lib/include/wbnoinvdintrin.h \
  C:/Development/zig/lib/include/cldemoteintrin.h \
  C:/Development/zig/lib/include/waitpkgintrin.h \
  C:/Development/zig/lib/include/movdirintrin.h \
  C:/Development/zig/lib/include/movrsintrin.h \
  C:/Development/zig/lib/include/movrs_avx10_2intrin.h \
  C:/Development/zig/lib/include/movrs_avx10_2_512intrin.h \
  C:/Development/zig/lib/include/pconfigintrin.h \
  C:/Development/zig/lib/include/sgxintrin.h \
  C:/Development/zig/lib/include/ptwriteintrin.h \
  C:/Development/zig/lib/include/invpcidintrin.h \
  C:/Development/zig/lib/include/keylockerintrin.h \
  C:/Development/zig/lib/include/amxintrin.h \
  C:/Development/zig/lib/include/amxfp16intrin.h \
  C:/Development/zig/lib/include/amxcomplexintrin.h \
  C:/Development/zig/lib/include/amxfp8intrin.h \
  C:/Development/zig/lib/include/amxtransposeintrin.h \
  C:/Development/zig/lib/include/amxmovrsintrin.h \
  C:/Development/zig/lib/include/amxmovrstransposeintrin.h \
  C:/Development/zig/lib/include/amxavx512intrin.h \
  C:/Development/zig/lib/include/amxtf32intrin.h \
  C:/Development/zig/lib/include/amxtf32transposeintrin.h \
  C:/Development/zig/lib/include/amxbf16transposeintrin.h \
  C:/Development/zig/lib/include/amxfp16transposeintrin.h \
  C:/Development/zig/lib/include/amxcomplextransposeintrin.h \
  C:/Development/zig/lib/include/avx512vp2intersectintrin.h \
  C:/Development/zig/lib/include/avx512vlvp2intersectintrin.h \
  C:/Development/zig/lib/include/avx10_2bf16intrin.h \
  C:/Development/zig/lib/include/avx10_2convertintrin.h \
  C:/Development/zig/lib/include/avx10_2copyintrin.h \
  C:/Development/zig/lib/include/avx10_2minmaxintrin.h \
  C:/Development/zig/lib/include/avx10_2niintrin.h \
  C:/Development/zig/lib/include/avx10_2satcvtdsintrin.h \
  C:/Development/zig/lib/include/avx10_2satcvtintrin.h \
  C:/Development/zig/lib/include/avx10_2_512bf16intrin.h \
  C:/Development/zig/lib/include/avx10_2_512convertintrin.h \
  C:/Development/zig/lib/include/avx10_2_512minmaxintrin.h \
  C:/Development/zig/lib/include/avx10_2_512niintrin.h \
  C:/Development/zig/lib/include/avx10_2_512satcvtdsintrin.h \
  C:/Development/zig/lib/include/avx10_2_512satcvtintrin.h \
  C:/Development/zig/lib/include/sm4evexintrin.h \
  C:/Development/zig/lib/include/enqcmdintrin.h \
  C:/Development/zig/lib/include/serializeintrin.h \
  C:/Development/zig/lib/include/tsxldtrkintrin.h \
  C:/Development/zig/lib/include/prfchwintrin.h \
  C:/Development/zig/lib/include/ammintrin.h \
  C:/Development/zig/lib/include/fma4intrin.h \
  C:/Development/zig/lib/include/xopintrin.h \
  C:/Development/zig/lib/include/tbmintrin.h \
  C:/Development/zig/lib/include/lwpintrin.h \
  C:/Development/zig/lib/include/mwaitxintrin.h \
  C:/Development/zig/lib/include/clzerointrin.h \
  C:/Development/zig/lib/include/rdpruintrin.h \
  C:/Development/zig/lib/libc/include/any-windows-any/pshpack4.h \
  C:/Development/zig/lib/libc/include/any-windows-any/poppack.h \
  C:/Development/zig/lib/libc/include/any-windows-any/pshpack2.h \
  C:/Development/zig/lib/libc/include/any-windows-any/pshpack8.h \
  C:/Development/zig/lib/libc/include/any-windows-any/ktmtypes.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winbase.h \
  C:/Development/zig/lib/libc/include/any-windows-any/apisetcconv.h \
  C:/Development/zig/lib/libc/include/any-windows-any/minwinbase.h \
  C:/Development/zig/lib/libc/include/any-windows-any/bemapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/debugapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/errhandlingapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/fibersapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/fileapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/handleapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/heapapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/ioapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/interlockedapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/jobapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/libloaderapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/memoryapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/namedpipeapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/namespaceapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/processenv.h \
  C:/Development/zig/lib/libc/include/any-windows-any/processthreadsapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/processtopologyapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/profileapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/realtimeapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/securityappcontainer.h \
  C:/Development/zig/lib/libc/include/any-windows-any/securitybaseapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/synchapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sysinfoapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/systemtopologyapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/threadpoolapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/threadpoollegacyapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/utilapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wow64apiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winerror.h \
  C:/Development/zig/lib/libc/include/any-windows-any/fltwinerror.h \
  C:/Development/zig/lib/libc/include/any-windows-any/timezoneapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wingdi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/pshpack1.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winuser.h \
  C:/Development/zig/lib/libc/include/any-windows-any/tvout.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winnls.h \
  C:/Development/zig/lib/libc/include/any-windows-any/datetimeapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/stringapiset.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wincon.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wincontypes.h \
  C:/Development/zig/lib/libc/include/any-windows-any/consoleapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/consoleapi2.h \
  C:/Development/zig/lib/libc/include/any-windows-any/consoleapi3.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winver.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winreg.h \
  C:/Development/zig/lib/libc/include/any-windows-any/reason.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winnetwk.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wnnc.h \
  C:/Development/zig/lib/libc/include/any-windows-any/virtdisk.h \
  C:/Development/zig/lib/libc/include/any-windows-any/cderr.h \
  C:/Development/zig/lib/libc/include/any-windows-any/dde.h \
  C:/Development/zig/lib/libc/include/any-windows-any/ddeml.h \
  C:/Development/zig/lib/libc/include/any-windows-any/dlgs.h \
  C:/Development/zig/lib/libc/include/any-windows-any/lzexpand.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mmsystem.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mmsyscom.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mciapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mmiscapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mmiscapi2.h \
  C:/Development/zig/lib/libc/include/any-windows-any/playsoundapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mmeapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/timeapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/joystickapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/nb30.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpc.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcdce.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcdcep.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcnsi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcnterr.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcasync.h \
  C:/Development/zig/lib/libc/include/any-windows-any/shellapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winperf.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winsock.h \
  C:/Development/zig/lib/libc/include/any-windows-any/_bsd_types.h \
  C:/Development/zig/lib/libc/include/any-windows-any/inaddr.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_socket_types.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_fd_types.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_ip_types.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_ip_mreq1.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_wsadata.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_xmitfile.h \
  C:/Development/zig/lib/libc/include/any-windows-any/psdk_inc/_wsa_errnos.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wincrypt.h \
  C:/Development/zig/lib/libc/include/any-windows-any/bcrypt.h \
  C:/Development/zig/lib/libc/include/any-windows-any/ncrypt.h \
  C:/Development/zig/lib/libc/include/any-windows-any/dpapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winefs.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winscard.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wtypes.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcndr.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcnsip.h \
  C:/Development/zig/lib/libc/include/any-windows-any/rpcsal.h \
  C:/Development/zig/lib/libc/include/any-windows-any/ole2.h \
  C:/Development/zig/lib/libc/include/any-windows-any/objbase.h \
  C:/Development/zig/lib/libc/include/any-windows-any/combaseapi.h \
  C:/Development/zig/lib/libc/include/any-windows-any/wtypesbase.h \
  C:/Development/zig/lib/libc/include/any-windows-any/unknwnbase.h \
  C:/Development/zig/lib/libc/include/any-windows-any/objidlbase.h \
  C:/Development/zig/lib/libc/include/any-windows-any/cguid.h \
  C:/Development/zig/lib/libc/include/any-windows-any/objidl.h \
  C:/Development/zig/lib/libc/include/any-windows-any/unknwn.h \
  C:/Development/zig/lib/libc/include/any-windows-any/urlmon.h \
  C:/Development/zig/lib/libc/include/any-windows-any/oleidl.h \
  C:/Development/zig/lib/libc/include/any-windows-any/servprov.h \
  C:/Development/zig/lib/libc/include/any-windows-any/msxml.h \
  C:/Development/zig/lib/libc/include/any-windows-any/oaidl.h \
  C:/Development/zig/lib/libc/include/any-windows-any/propidl.h \
  C:/Development/zig/lib/libc/include/any-windows-any/oleauto.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winioctl.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winsmcrd.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winspool.h \
  C:/Development/zig/lib/libc/include/any-windows-any/prsht.h \
  C:/Development/zig/lib/libc/include/any-windows-any/commdlg.h \
  C:/Development/zig/lib/libc/include/any-windows-any/stralign.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sec_api/stralign_s.h \
  C:/Development/zig/lib/libc/include/any-windows-any/winsvc.h \
  C:/Development/zig/lib/libc/include/any-windows-any/mcx.h \
  C:/Development/zig/lib/libc/include/any-windows-any/imm.h \
  C:/Development/zig/lib/libc/include/any-windows-any/conio.h \
  C:/Development/zig/lib/include/stddef.h \
  C:/Development/zig/lib/libc/include/any-windows-any/stddef.h \
  C:/Development/zig/lib/include/__stddef_header_macro.h \
  C:/Development/zig/lib/include/__stddef_ptrdiff_t.h \
  C:/Development/zig/lib/include/__stddef_size_t.h \
  C:/Development/zig/lib/include/__stddef_wchar_t.h \
  C:/Development/zig/lib/include/__stddef_null.h \
  C:/Development/zig/lib/include/__stddef_offsetof.h \
  C:/Development/zig/lib/libc/include/any-windows-any/sec_api/conio_s.h \
  C:/Development/zig/lib/libc/include/any-windows-any/direct.h \
  C:/Development/zig/lib/libc/include/any-windows-any/io.h \
  ddgbas/retro_ide.h C:/Development/zig/lib/include/stdint.h \
  C:/Development/zig/lib/libc/include/any-windows-any/stdint.h \
  C:/Development/zig/lib/include/__stddef_wint_t.h \
  C:/Development/zig/lib/include/stdbool.h ddgbas/ide_security.h \
  ddgbas/ide_mouse.h
