# xonoBASIC Technical Architecture

## Core Architecture

xonoBASIC follows a layered architecture:

1. **Application Layer**: <PERSON><PERSON><PERSON>, Variables Manager, Commands Processor, CGI/Web Handler
2. **Enhanced Feature Layer**: Database (135+ cmds), Graphics (38+ cmds), Arrays (5D support), Performance Monitoring
3. **Core Libraries Layer**: SQLite 3.50.3, libgd 2.3.3, SFL 3.31, System Libraries
4. **Platform Abstraction Layer**: Windows (Native), Linux/macOS/FreeBSD (Cross-compiled)

## Key Components

### Database Layer
- SQLite 3.50.3 with JSON1, FTS5, R-Tree extensions
- Multiple connection support with connection pooling
- Prepared statements for security and performance
- Advanced transaction management

### Graphics Layer
- libgd 2.3.3 with WebP, PNG, JPEG support
- Advanced image processing and filters
- Memory-efficient rendering pipeline
- Professional graphics capabilities

### Parser and Runtime
- Multi-dimensional array support (1D-5D)
- Enhanced scientific notation support
- Optimized memory management
- Comprehensive error handling

## Build System

- **Primary**: Zig compiler for cross-platform builds
- **Targets**: Windows (x86/x64), Linux (x64/ARM64), macOS (x64/ARM64), FreeBSD (x64)
- **Libraries**: Static linking preferred for portability
- **Optimization**: ReleaseFast mode with LTO for Windows

## File Structure

- `ddgbas/` - Core xonoBASIC source code
- `sfl/` - SFL library (stable, no changes needed)
- `gd2/`, `jpglib/`, `pnglib/`, `zlib/` - Graphics libraries
- `build.zig` - Zig build configuration
- `*.bas` - Comprehensive test suite (40+ files)